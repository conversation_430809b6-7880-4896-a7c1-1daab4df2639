# -*- coding: utf-8 -*-
"""
Created on Mon Jun 13 13:23:20 2022

@author: gbonavi<PERSON>
"""

import socket
import sys
import struct
import time
import select

#http://pymotw.com/2/socket/tcp.html
#https://docs.python.org/3.6/howto/sockets.html#using-a-socket

preHex = "" #"'H"
midHex = "" #"'" + preHex
postHex = "" #"'"

AUTOMATIC_SEND = 1
MANUAL_SEND = 2

class Server:

    def __init__(self, ip, port, inputMessageLength, outputMessageLength, checkReceivedBytesSize, cleanReceivedData, receiveTimeout):
        self.ip = ip
        self.port = port
        self.inputMessageLength = inputMessageLength
        self.outputMessageLength = outputMessageLength
        
        self.checkReceivedBytesSize = checkReceivedBytesSize
        self.cleanReceivedData = cleanReceivedData
        
        self.receiveTimeout = receiveTimeout
        
        try:
            self.connection.close()
        except:
            print ('')
        
    def createSocket(self):
        # Create a TCP/IP socket
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        
        # if self.receiveTimeout > 0:
        #     self.sock.setblocking(0)
        print ('Server - socket created')
    
    def bindSocket(self): 
        # Bind the socket to the port
        self.server_address = (self.ip, self.port) #'localhost', 10000
        print ('Server - starting up on %s port %s' % self.server_address)
        self.sock.bind(self.server_address)
        print ('Server - socket bind')
    
    def communicationInit(self):
        self.createSocket()
        self.bindSocket()        
        
    def socketSend(self, msg):
        
        self.totalBytesSent = 0
        
        print()
        print('Server - sending len %s' % len(msg))
        print('Server - sending %s' % msg)
        while self.totalBytesSent < self.outputMessageLength:
            sent = self.connection.send(msg[self.totalBytesSent:])
            if sent == 0:
                raise RuntimeError("socket connection broken")
            self.totalBytesSent = self.totalBytesSent + sent
            print('Server - bytes sent %d' % self.totalBytesSent)
            print()
    
    def socketReceive(self):
        
        chunks = []
        self.totalBytesReceived = 0
        print()
        
        try:
            if self.receiveTimeout > 0:
                print("Server - Waiting %s seconds to receive a message..."% self.receiveTimeout)
                self.connection.settimeout(self.receiveTimeout)
                
            if not self.checkReceivedBytesSize:
                chunk = self.connection.recv(min(self.inputMessageLength - self.totalBytesReceived, 2048))
                chunks.append(chunk)
                self.totalBytesReceived = self.totalBytesReceived + len(chunk)
            else:
                while self.totalBytesReceived < self.inputMessageLength:
                    chunk = self.connection.recv(min(self.inputMessageLength - self.totalBytesReceived, 2048))
                    if chunk == b'':
                        raise RuntimeError("socket connection broken")
                    chunks.append(chunk)
                    self.totalBytesReceived = self.totalBytesReceived + len(chunk)
        
            
            print('Server - bytes received %d' % self.totalBytesReceived)
            return b''.join(chunks)
        except socket.timeout as e:
            print("Receive Timeout")
        
    def loopSocketSendRec(self, msg):
        
        # Listen for incoming connections
        self.sock.listen(1)
        print ('Server - socket listening')
        
        while True:
            # Wait for a connection
            print ('Server - waiting for a connection')
            self.connection, self.client_address = self.sock.accept()
            
            try:
                print ('Server - connection from', self.client_address)
        
                while True:
                    self.dataReceived = self.socketReceive()
                    
                    if self.cleanReceivedData:
                        dataReceivedCleaned = str(self.dataReceived).replace("\\x", " - ")
                    else:
                        dataReceivedCleaned = str(self.dataReceived)
                        
                    print ('Server - received "%s"' % dataReceivedCleaned)
                    if self.dataReceived:
                        print ('Server - sending data to the client')
                        self.socketSend(msg)
                    else:
                        print ('Server - no more data from', self.client_address)
                        break
                    
            finally:
                # Clean up the connection
                self.connection.close()

def swap16(val):
    return struct.pack('>L', val)[1]

def packByte(val):
    return preHex + ''.join('{:02X}'.format(val)) + postHex

def packInt16(val):
    _hex_to_be_showed = ''.join('{:04X}'.format(val))
    _hexWithPrefix = preHex + midHex.join(_hex_to_be_showed[i:i+2] for i in range(0, len(_hex_to_be_showed), 2))
    return  _hexWithPrefix + postHex

def packString(_str, maxLen): 
    
    _hex_to_be_showed = ""
    for _c in _str:
        _hex_to_be_showed += format(ord(_c), '01x').upper()
        
    actLen = len(_str)
    for i in range(actLen, maxLen):
        _hex_to_be_showed += "00"
        
    _hexWithPrefix = preHex + midHex.join(_hex_to_be_showed[i:i+2] for i in range(0, len(_hex_to_be_showed), 2))
    return _hexWithPrefix + postHex

def castToByteArray(_str):
    return bytearray.fromhex(_str)

def showResult(_str, splitChar, _HexStr):
    
    Hercules_msg = _str.replace(splitChar, "")
    print("Put this message in Hercules:\n")
    print(Hercules_msg)
    print("\n--------------------------------------------------\n")
    print("Message len %d bytes"% int(len(Hercules_msg)/2))
    print("Message header has %d bytes"% 3)
    print("Message data has %d bytes"% (int(len(Hercules_msg)/2)-3))
    
    print("\n--------------------------------------------------\n")
    print("Message splitted for each variable value\n")
    print(_HexStr.replace(splitChar, "\n"))
