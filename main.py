# -*- coding: utf-8 -*-
"""
Created on Mon Jun 13 13:22:42 2022

@author: gbonavita
"""

from Server import *

# =============================================================================
# Define socket input output size
# =============================================================================
serverIp = '************' #'localhost'
ServerPort = 4999
inputMessageLength = 130 #Bytes
outputMessageLength = 130 #Bytes

receiveTimeout = 10

headerBytes = 3

lifeCounter = 0
sendMode = AUTOMATIC_SEND  #AUTOMATIC_SEND #MANUAL_SEND

# =============================================================================
# Define send message
# =============================================================================


def buildSendMessage1():
    
    splitChar = ""

    #Put here the value of your message that you want to simulate
    #Header
    _HexStr = packString("0200", 4) + splitChar
    _HexStr += packString("01", 2) + splitChar
    
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo
    _HexStr += packByte(0) + splitChar       #pack Data   - Packet event counter Echo

    #Message
    _HexStr += packString("0001", 4) + splitChar
    _HexStr += packString("1", 1) + splitChar #1=L2 ready, 2=L2 fault (not ready)

    for i in range(23, 200):
        _HexStr += packByte(0) + splitChar #pack Data   - spare
    
    return _HexStr

def buildSendMessage2():
    outputMessageDataLength = outputMessageLength - headerBytes

    splitChar = ""

    #Put here the value of your message that you want to simulate

    #Header
    _HexStr = packByte(1) + splitChar
    _HexStr += packByte(128) + splitChar

    _HexStr += packString("1", 1) + splitChar                    # WATCHDOG COUNTER

    _HexStr += packByte(0) + splitChar                                       # pack Data   - Packet event counter Echo

    _HexStr += packString("0", 1) + splitChar                    # PRINTER STATUS
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare

    # DATA TO BE SENT TO ROBOT
    _HexStr += packString("1", 1) + splitChar                    # LABEL ALARMS
    _HexStr += packString("3", 1) + splitChar                    # COIL DATA FILE ALARMS
    _HexStr += packString("4", 1) + splitChar                    # PRINTER ALARMS
    _HexStr += packString("5", 1) + splitChar                    # TOTAL LABELS TO PRINT
    _HexStr += packString("6", 1) + splitChar                    # CURRENT LABEL PAGE TO PRINT

    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare
    _HexStr += packByte(0) + splitChar                                       # pack Data   - spare

    for i in range(19, outputMessageLength+1):

        _HexStr += packByte(0) + splitChar                                   # pack Data  - spare
    
    return _HexStr
    
# =============================================================================
# Server management
# =============================================================================

checkReceivedBytesSize = False
cleanReceivedData = False

if sendMode == AUTOMATIC_SEND:
    receiveTimeout = 0
    
MyTCPIP_Server = Server(serverIp, ServerPort, inputMessageLength, outputMessageLength, checkReceivedBytesSize, cleanReceivedData, receiveTimeout)
    
try:
    MyTCPIP_Server.connection.close()
except:
    print()

#Connection loop
while True:

    
    try:
        MyTCPIP_Server.communicationInit()
        
        # Listen for incoming connections
        MyTCPIP_Server.sock.listen(1)
        print ('Server - socket listening')
        
        try:
            # Wait for a connection
            print ('Server - waiting for a connection')
            MyTCPIP_Server.connection, MyTCPIP_Server.client_address = MyTCPIP_Server.sock.accept()
            
            print ('Server - Client connection from', MyTCPIP_Server.client_address)
            
            #Send recieve loop
            while True:
                
                if sendMode == AUTOMATIC_SEND:
                    print('\n# ===================================================================\n')
                    # ===================================================================
                    #   Receive managment
                    # ===================================================================
                    #if checkReceivedBytesSize = True the server must receive inputMessageLength bytes
                    #to move to the send else it will answer immediately
                    MyTCPIP_Server.dataReceived = MyTCPIP_Server.socketReceive()
                    
                    print('\n# ***********************')
                    print ('\nServer - data received from the client\n')
                    
                    if MyTCPIP_Server.cleanReceivedData:
                        dataReceived = str(MyTCPIP_Server.dataReceived).replace("\\x", " - ")
                    else:
                        dataReceived = str(MyTCPIP_Server.dataReceived)
                        
                    print ('Server - received "%s"' % dataReceived)
                    
                    # ===================================================================
                    #   Send managment
                    # ===================================================================
                    sendMsg = castToByteArray(buildSendMessage2())
                    
                    if MyTCPIP_Server.dataReceived: # if we received data send immediately the answer
                        print('\n# ***********************')
                        print ('\nServer - sending data to the client')
                        MyTCPIP_Server.socketSend(sendMsg)
                    else:
                        print ('Server - no more data from', MyTCPIP_Server.client_address)
                        break
                    
                if sendMode == MANUAL_SEND:
                    
                    print('\n# ===================================================================\n')
                    tmp = input('Wait Enter Key to send message...')
                    
                    # ===================================================================
                    #   Send managment
                    # ===================================================================
                    sendMsg = castToByteArray(buildSendMessage2())
                    
                    print('\n# ***********************')
                    print ('\nServer - sending data to the client')
                    MyTCPIP_Server.socketSend(sendMsg)
                    
                    
                    print('\n# ===================================================================\n')
                    # ===================================================================
                    #   Receive managment
                    # ===================================================================
                    #if checkReceivedBytesSize = True the server must receive inputMessageLength bytes
                    #to move to the send else it will answer immediately
                    MyTCPIP_Server.dataReceived = MyTCPIP_Server.socketReceive()
                    
                    print('\n# ***********************')
                    print ('\nServer - data received from the client\n')
                    
                    if MyTCPIP_Server.cleanReceivedData:
                        dataReceived = str(MyTCPIP_Server.dataReceived).replace("\\x", " - ")
                    else:
                        dataReceived = str(MyTCPIP_Server.dataReceived)
                        
                    print ('Server - received "%s"' % dataReceived)
                    
                    time.sleep(1) #to be sure wait 1 sec.
                    
        finally:
            # Clean up the connection
            MyTCPIP_Server.connection.close()
        
    except OSError as error :
        print(error)
        print('\n# ===================================================================')
        print('\tPut server IP %s in your network card motherfucker' % serverIp)
        print('# ===================================================================')
        break
    except:
        continue
        
#Message to test receive
#0101340200010000000000000001000005AA049C000204054142434445464748494A313233343536373839306162636465666768694F57316162636465666768694142434445464748494A313233343536373839304F5732616263646566676869313233343536373839304142434445464748494A4F57334142434445464748494A616263646566676869313233343536373839304F57344142434445464748494A313233343536373839306162636465666768694F57356162636465666768694142434445464748494A31323334353637383930465331616263646566676869313233343536373839304142434445464748494A4653324142434445464748494A6162636465666768693132333435363738393046533306000000000000000000000000000000000000000000000000000000000000